<template>
  <div class="math-keyboard">
    <div class="keyboard-section" v-for="section in keyboardSections" :key="section.title">
      <h4 class="section-title">{{ section.title }}</h4>
      <div class="key-grid" :class="section.gridClass">
        <button
          v-for="key in section.keys"
          :key="key.symbol"
          @click="insertSymbol(key.symbol)"
          class="math-key"
          :class="key.category"
          :title="key.label"
          type="button"
        >
          {{ key.symbol }}
        </button>
      </div>
    </div>

    <!-- Action buttons -->
    <div class="action-buttons">
      <UButton @click="clearInput" variant="outline" size="sm">
        Clear
      </UButton>
      <UButton @click="backspace" variant="outline" size="sm">
        ⌫
      </UButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { MathKeyboardKey } from '~/types/game'

interface Props {
  modelValue: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Keyboard layout organized by sections
const keyboardSections = computed(() => [
  {
    title: 'Numbers',
    gridClass: 'numbers-grid',
    keys: [
      { symbol: '1', label: 'One', category: 'number' },
      { symbol: '2', label: 'Two', category: 'number' },
      { symbol: '3', label: 'Three', category: 'number' },
      { symbol: '4', label: 'Four', category: 'number' },
      { symbol: '5', label: 'Five', category: 'number' },
      { symbol: '6', label: 'Six', category: 'number' },
      { symbol: '7', label: 'Seven', category: 'number' },
      { symbol: '8', label: 'Eight', category: 'number' },
      { symbol: '9', label: 'Nine', category: 'number' },
      { symbol: '0', label: 'Zero', category: 'number' },
      { symbol: '-', label: 'Negative', category: 'number' },
      { symbol: '.', label: 'Decimal', category: 'number' }
    ] as MathKeyboardKey[]
  },
  {
    title: 'Variables & Operators',
    gridClass: 'operators-grid',
    keys: [
      { symbol: 'x', label: 'Variable x', category: 'operator' },
      { symbol: 'y', label: 'Variable y', category: 'operator' },
      { symbol: '+', label: 'Plus', category: 'operator' },
      { symbol: '=', label: 'Equals', category: 'operator' }
    ] as MathKeyboardKey[]
  },
  {
    title: 'Inequalities',
    gridClass: 'inequalities-grid',
    keys: [
      { symbol: '<', label: 'Less than', category: 'inequality' },
      { symbol: '>', label: 'Greater than', category: 'inequality' },
      { symbol: '≤', label: 'Less than or equal', category: 'inequality' },
      { symbol: '≥', label: 'Greater than or equal', category: 'inequality' }
    ] as MathKeyboardKey[]
  },
  {
    title: 'Interval Notation',
    gridClass: 'interval-grid',
    keys: [
      { symbol: '(', label: 'Open parenthesis', category: 'interval' },
      { symbol: ')', label: 'Close parenthesis', category: 'interval' },
      { symbol: '[', label: 'Open bracket', category: 'interval' },
      { symbol: ']', label: 'Close bracket', category: 'interval' },
      { symbol: ',', label: 'Comma', category: 'interval' },
      { symbol: '∞', label: 'Infinity', category: 'interval' },
      { symbol: '∪', label: 'Union', category: 'interval' },
      { symbol: '∩', label: 'Intersection', category: 'interval' }
    ] as MathKeyboardKey[]
  },
  {
    title: 'Set Notation',
    gridClass: 'set-grid',
    keys: [
      { symbol: '{', label: 'Open brace', category: 'set' },
      { symbol: '}', label: 'Close brace', category: 'set' },
      { symbol: '|', label: 'Such that', category: 'set' }
    ] as MathKeyboardKey[]
  }
])

const insertSymbol = (symbol: string) => {
  const newValue = props.modelValue + symbol
  emit('update:modelValue', newValue)
}

const clearInput = () => {
  emit('update:modelValue', '')
}

const backspace = () => {
  const newValue = props.modelValue.slice(0, -1)
  emit('update:modelValue', newValue)
}
</script>

<style scoped>
.math-keyboard {
  @apply bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700;
  @apply space-y-4;
}

.keyboard-section {
  @apply space-y-2;
}

.section-title {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

.key-grid {
  @apply grid gap-2;
}

.numbers-grid {
  grid-template-columns: repeat(4, 1fr);
}

.operators-grid {
  grid-template-columns: repeat(4, 1fr);
}

.inequalities-grid {
  grid-template-columns: repeat(4, 1fr);
}

.interval-grid {
  grid-template-columns: repeat(4, 1fr);
}

.set-grid {
  grid-template-columns: repeat(3, 1fr);
}

.math-key {
  @apply px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded;
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
  @apply hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1;
  @apply active:scale-95 transform;
}

.math-key.number {
  @apply bg-blue-50 dark:bg-blue-900/30 border-blue-200 dark:border-blue-700;
  @apply text-blue-700 dark:text-blue-300;
}

.math-key.operator {
  @apply bg-purple-50 dark:bg-purple-900/30 border-purple-200 dark:border-purple-700;
  @apply text-purple-700 dark:text-purple-300;
}

.math-key.inequality {
  @apply bg-red-50 dark:bg-red-900/30 border-red-200 dark:border-red-700;
  @apply text-red-700 dark:text-red-300;
}

.math-key.interval {
  @apply bg-green-50 dark:bg-green-900/30 border-green-200 dark:border-green-700;
  @apply text-green-700 dark:text-green-300;
}

.math-key.set {
  @apply bg-orange-50 dark:bg-orange-900/30 border-orange-200 dark:border-orange-700;
  @apply text-orange-700 dark:text-orange-300;
}

.action-buttons {
  @apply flex space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700;
}

/* Mobile responsive */
@media (max-width: 640px) {
  .numbers-grid,
  .operators-grid,
  .inequalities-grid,
  .interval-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .math-key {
    @apply px-2 py-1 text-xs;
  }
}
</style>