{"name": "func-boundaries", "private": true, "type": "module", "packageManager": "pnpm@10.13.1", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "typecheck": "nuxt typecheck", "format": "biome format --write .", "lint": "biome lint --write ."}, "dependencies": {"@nuxt/ui": "^3.2.0", "@pinia/nuxt": "^0.11.1", "@vueuse/motion": "^3.0.3", "nuxt": "^4.0.0", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@biomejs/biome": "^2.1.1", "typescript": "^5.8.3", "vue-tsc": "^3.0.1"}}