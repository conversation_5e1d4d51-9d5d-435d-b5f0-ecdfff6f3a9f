import type {
  NotationProblem,
  DomainRangeProblem,
  NotationType,
  FunctionType,
  ProblemGeneratorOptions
} from '~/types/game'

/**
 * Generate random integer within range
 */
function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * Generate random coefficient (avoiding 0)
 */
function randomCoeff(): number {
  const coeff = randomInt(-5, 5)
  return coeff === 0 ? 1 : coeff
}

/**
 * Generate notation conversion problems
 */
export function generateNotationProblem(
  sourceNotation: NotationType,
  targetNotation: NotationType,
  options: ProblemGeneratorOptions = {}
): NotationProblem {
  const id = `notation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

  // Generate base inequality
  const variable = 'x'
  const value = randomInt(-10, 10)
  const operators = ['>', '<', '≥', '≤']
  const operator = operators[randomInt(0, operators.length - 1)]

  let problem = ''
  let correctAnswers: string[] = []

  if (sourceNotation === 'algebraic') {
    problem = `${variable} ${operator} ${value}`

    if (targetNotation === 'interval') {
      correctAnswers = convertAlgebraicToInterval(variable, operator, value)
    } else if (targetNotation === 'set-builder') {
      correctAnswers = convertAlgebraicToSetBuilder(variable, operator, value)
    }
  } else if (sourceNotation === 'interval') {
    const intervalNotation = generateIntervalNotation()
    problem = intervalNotation.notation

    if (targetNotation === 'algebraic') {
      correctAnswers = [intervalNotation.algebraic]
    } else if (targetNotation === 'set-builder') {
      correctAnswers = [intervalNotation.setBuilder]
    }
  } else if (sourceNotation === 'set-builder') {
    const setBuilderNotation = generateSetBuilderNotation()
    problem = setBuilderNotation.notation

    if (targetNotation === 'algebraic') {
      correctAnswers = [setBuilderNotation.algebraic]
    } else if (targetNotation === 'interval') {
      correctAnswers = [setBuilderNotation.interval]
    }
  }

  return {
    id,
    type: 'notation-conversion',
    sourceNotation,
    targetNotation,
    problem: `Convert to ${targetNotation.replace('-', ' ')} notation: ${problem}`,
    correctAnswers,
    hint: options.includeHints ? generateNotationHint(targetNotation) : undefined
  }
}

/**
 * Convert algebraic inequality to interval notation
 */
function convertAlgebraicToInterval(variable: string, operator: string, value: number): string[] {
  switch (operator) {
    case '>':
      return [`(${value}, ∞)`]
    case '<':
      return [`(-∞, ${value})`]
    case '≥':
      return [`[${value}, ∞)`]
    case '≤':
      return [`(-∞, ${value}]`]
    default:
      return []
  }
}

/**
 * Convert algebraic inequality to set-builder notation
 */
function convertAlgebraicToSetBuilder(variable: string, operator: string, value: number): string[] {
  return [`{${variable} | ${variable} ${operator} ${value}}`]
}

/**
 * Generate interval notation with corresponding algebraic and set-builder forms
 */
function generateIntervalNotation() {
  const value = randomInt(-10, 10)
  const types = ['open-right', 'open-left', 'closed-right', 'closed-left']
  const type = types[randomInt(0, types.length - 1)]

  switch (type) {
    case 'open-right':
      return {
        notation: `(${value}, ∞)`,
        algebraic: `x > ${value}`,
        setBuilder: `{x | x > ${value}}`
      }
    case 'open-left':
      return {
        notation: `(-∞, ${value})`,
        algebraic: `x < ${value}`,
        setBuilder: `{x | x < ${value}}`
      }
    case 'closed-right':
      return {
        notation: `[${value}, ∞)`,
        algebraic: `x ≥ ${value}`,
        setBuilder: `{x | x ≥ ${value}}`
      }
    case 'closed-left':
      return {
        notation: `(-∞, ${value}]`,
        algebraic: `x ≤ ${value}`,
        setBuilder: `{x | x ≤ ${value}}`
      }
    default:
      return {
        notation: `(${value}, ∞)`,
        algebraic: `x > ${value}`,
        setBuilder: `{x | x > ${value}}`
      }
  }
}

/**
 * Generate set-builder notation with corresponding forms
 */
function generateSetBuilderNotation() {
  const value = randomInt(-10, 10)
  const operators = ['>', '<', '≥', '≤']
  const operator = operators[randomInt(0, operators.length - 1)]

  const notation = `{x | x ${operator} ${value}}`
  const algebraic = `x ${operator} ${value}`

  let interval = ''
  switch (operator) {
    case '>':
      interval = `(${value}, ∞)`
      break
    case '<':
      interval = `(-∞, ${value})`
      break
    case '≥':
      interval = `[${value}, ∞)`
      break
    case '≤':
      interval = `(-∞, ${value}]`
      break
  }

  return { notation, algebraic, interval }
}

/**
 * Generate hints for notation conversion
 */
function generateNotationHint(targetNotation: NotationType): string {
  switch (targetNotation) {
    case 'interval':
      return 'Use parentheses ( ) for strict inequalities and brackets [ ] for inclusive inequalities. Use ∞ for infinity.'
    case 'set-builder':
      return 'Use the format {x | condition} where x is the variable and condition describes the constraint.'
    case 'algebraic':
      return 'Write as a simple inequality using <, >, ≤, or ≥ symbols.'
    default:
      return 'Check the target notation format carefully.'
  }
}

/**
 * Generate domain and range problems
 */
export function generateDomainRangeProblem(
  functionType: FunctionType,
  questionType: 'domain' | 'range' | 'both',
  options: ProblemGeneratorOptions = {}
): DomainRangeProblem {
  const id = `domain-range-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

  const functionData = generateFunction(functionType)

  return {
    id,
    type: 'domain-range',
    functionType,
    equation: functionData.equation,
    questionType,
    correctDomain: functionData.domain,
    correctRange: functionData.range,
    hint: options.includeHints ? generateDomainRangeHint(functionType, questionType) : undefined
  }
}

/**
 * Generate function equations with their domains and ranges
 */
function generateFunction(type: FunctionType) {
  const a = randomCoeff()
  const b = randomInt(-5, 5)
  const c = randomInt(-5, 5)

  switch (type) {
    case 'linear':
      return {
        equation: `f(x) = ${a}x + ${b}`,
        domain: ['(-∞, ∞)'],
        range: ['(-∞, ∞)']
      }

    case 'quadratic':
      const vertex_y = c - (b * b) / (4 * a)
      if (a > 0) {
        return {
          equation: `f(x) = ${a}x² + ${b}x + ${c}`,
          domain: ['(-∞, ∞)'],
          range: [`[${vertex_y}, ∞)`]
        }
      } else {
        return {
          equation: `f(x) = ${a}x² + ${b}x + ${c}`,
          domain: ['(-∞, ∞)'],
          range: [`(-∞, ${vertex_y}]`]
        }
      }

    case 'cubic':
      return {
        equation: `f(x) = ${a}x³ + ${b}x² + ${c}x`,
        domain: ['(-∞, ∞)'],
        range: ['(-∞, ∞)']
      }

    case 'absolute':
      const h = randomInt(-3, 3)
      const k = randomInt(-3, 3)
      return {
        equation: `f(x) = ${a}|x - ${h}| + ${k}`,
        domain: ['(-∞, ∞)'],
        range: a > 0 ? [`[${k}, ∞)`] : [`(-∞, ${k}]`]
      }

    case 'square-root':
      const h_sqrt = randomInt(0, 5)
      const k_sqrt = randomInt(-2, 2)
      return {
        equation: `f(x) = ${a}√(x - ${h_sqrt}) + ${k_sqrt}`,
        domain: [`[${h_sqrt}, ∞)`],
        range: a > 0 ? [`[${k_sqrt}, ∞)`] : [`(-∞, ${k_sqrt}]`]
      }

    default:
      return {
        equation: 'f(x) = x',
        domain: ['(-∞, ∞)'],
        range: ['(-∞, ∞)']
      }
  }
}

/**
 * Generate hints for domain and range problems
 */
function generateDomainRangeHint(functionType: FunctionType, questionType: 'domain' | 'range' | 'both'): string {
  const domainHints = {
    linear: 'Linear functions are defined for all real numbers.',
    quadratic: 'Quadratic functions are defined for all real numbers.',
    cubic: 'Cubic functions are defined for all real numbers.',
    absolute: 'Absolute value functions are defined for all real numbers.',
    'square-root': 'Square root functions require the expression under the radical to be non-negative.'
  }

  const rangeHints = {
    linear: 'Linear functions with non-zero slope have range of all real numbers.',
    quadratic: 'Parabolas opening upward have range [vertex_y, ∞), downward have range (-∞, vertex_y].',
    cubic: 'Cubic functions typically have range of all real numbers.',
    absolute: 'Absolute value functions have range starting from the vertex y-coordinate.',
    'square-root': 'Square root functions have range starting from the y-coordinate of the starting point.'
  }

  if (questionType === 'domain') {
    return domainHints[functionType]
  } else if (questionType === 'range') {
    return rangeHints[functionType]
  } else {
    return `Domain: ${domainHints[functionType]} Range: ${rangeHints[functionType]}`
  }
}