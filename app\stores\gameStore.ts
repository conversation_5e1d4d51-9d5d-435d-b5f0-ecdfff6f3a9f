import type {
  GameState,
  GameMode,
  GameItem,
  Problem,
  NotationType,
  FunctionType,
  GameStats
} from '~/types/game'
import { generateNotationProblem, generateDomainRangeProblem } from '~/utils/problemGenerator'

export const useGameStore = defineStore('game', () => {
  // State
  const state = ref<GameState>({
    currentMode: null,
    items: {},
    currentProblem: null,
    currentAnswer: '',
    showModal: false,
    totalCompleted: 0,
    startTime: undefined,
    endTime: undefined
  })

  // Getters
  const isGameActive = computed(() => state.value.currentMode !== null)
  const completedItems = computed(() =>
    Object.values(state.value.items).filter(item => item.completed)
  )
  const completionPercentage = computed(() =>
    Math.round((state.value.totalCompleted / 37) * 100)
  )
  const isGameComplete = computed(() => state.value.totalCompleted === 37)

  // Game statistics
  const gameStats = computed((): GameStats => {
    const items = Object.values(state.value.items)
    const totalAttempts = items.reduce((sum, item) => sum + item.attempts, 0)
    const correctAnswers = items.filter(item => item.completed).length

    let averageTime = 0
    if (state.value.startTime && state.value.endTime) {
      averageTime = (state.value.endTime.getTime() - state.value.startTime.getTime()) / correctAnswers
    }

    return {
      totalAttempts,
      correctAnswers,
      averageTime,
      completionRate: correctAnswers / 37
    }
  })

  // Actions
  const initializeGame = (mode: GameMode) => {
    state.value.currentMode = mode
    state.value.startTime = new Date()
    state.value.endTime = undefined
    state.value.totalCompleted = 0

    // Initialize all 37 game items
    const items: Record<number, GameItem> = {}
    for (let i = 1; i <= 37; i++) {
      items[i] = {
        id: i,
        completed: false,
        attempts: 0,
        lastAttemptTime: undefined
      }
    }
    state.value.items = items
  }

  const selectItem = (itemId: number) => {
    if (state.value.items[itemId]?.completed) {
      return // Don't allow selecting completed items
    }

    // Generate appropriate problem based on game mode
    if (state.value.currentMode === 'notation-conversion') {
      state.value.currentProblem = generateNotationConversionProblem()
    } else if (state.value.currentMode === 'domain-range') {
      state.value.currentProblem = generateDomainRangeProblem('linear', 'domain')
    }

    state.value.currentAnswer = ''
    state.value.showModal = true
  }

  const generateNotationConversionProblem = (): Problem => {
    const conversions = [
      { source: 'algebraic' as NotationType, target: 'interval' as NotationType },
      { source: 'algebraic' as NotationType, target: 'set-builder' as NotationType },
      { source: 'interval' as NotationType, target: 'algebraic' as NotationType },
      { source: 'interval' as NotationType, target: 'set-builder' as NotationType },
      { source: 'set-builder' as NotationType, target: 'algebraic' as NotationType },
      { source: 'set-builder' as NotationType, target: 'interval' as NotationType }
    ]

    const conversion = conversions[Math.floor(Math.random() * conversions.length)]
    return generateNotationProblem(conversion.source, conversion.target, { includeHints: true })
  }

  const generateDomainRangeProb = (): Problem => {
    const functionTypes: FunctionType[] = ['linear', 'quadratic', 'cubic', 'absolute', 'square-root']
    const questionTypes = ['domain', 'range'] as const

    const functionType = functionTypes[Math.floor(Math.random() * functionTypes.length)]
    const questionType = questionTypes[Math.floor(Math.random() * questionTypes.length)]

    return generateDomainRangeProblem(functionType, questionType, { includeHints: true })
  }

  const updateAnswer = (answer: string) => {
    state.value.currentAnswer = answer
  }

  const submitAnswer = (isCorrect: boolean, currentItemId?: number) => {
    if (!state.value.currentProblem || !currentItemId) return

    const item = state.value.items[currentItemId]
    if (!item) return

    item.attempts++
    item.lastAttemptTime = new Date()

    if (isCorrect) {
      item.completed = true
      state.value.totalCompleted++
      state.value.showModal = false
      state.value.currentProblem = null
      state.value.currentAnswer = ''

      // Check if game is complete
      if (state.value.totalCompleted === 37) {
        state.value.endTime = new Date()
      }
    } else {
      // Generate new problem for incorrect answer
      if (state.value.currentMode === 'notation-conversion') {
        state.value.currentProblem = generateNotationConversionProblem()
      } else if (state.value.currentMode === 'domain-range') {
        state.value.currentProblem = generateDomainRangeProb()
      }
      state.value.currentAnswer = ''
    }
  }

  const closeModal = () => {
    state.value.showModal = false
    state.value.currentProblem = null
    state.value.currentAnswer = ''
  }

  const resetGame = () => {
    state.value.currentMode = null
    state.value.items = {}
    state.value.currentProblem = null
    state.value.currentAnswer = ''
    state.value.showModal = false
    state.value.totalCompleted = 0
    state.value.startTime = undefined
    state.value.endTime = undefined
  }

  return {
    // State
    state: readonly(state),

    // Getters
    isGameActive,
    completedItems,
    completionPercentage,
    isGameComplete,
    gameStats,

    // Actions
    initializeGame,
    selectItem,
    updateAnswer,
    submitAnswer,
    closeModal,
    resetGame
  }
})