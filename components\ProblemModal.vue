<template>
  <UModal :model-value="true" @update:model-value="handleClose" :ui="{ width: 'sm:max-w-2xl' }">
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ getModalTitle() }}
          </h3>
          <UButton
            @click="handleClose"
            variant="ghost"
            icon="i-heroicons-x-mark"
            size="sm"
          />
        </div>
      </template>

      <div class="space-y-6">
        <!-- Problem Statement -->
        <div class="problem-statement">
          <div class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Problem:
          </div>
          <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
            <div class="text-blue-900 dark:text-blue-100 font-mono text-lg">
              {{ problem.problem }}
            </div>
          </div>
        </div>

        <!-- Function Equation (for domain-range problems) -->
        <div v-if="problem.type === 'domain-range'" class="function-display">
          <div class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Function:
          </div>
          <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
            <div class="text-green-900 dark:text-green-100 font-mono text-xl text-center">
              {{ (problem as DomainRangeProblem).equation }}
            </div>
          </div>
        </div>

        <!-- Answer Input -->
        <div class="answer-section">
          <div class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Your Answer:
          </div>
          <UInput
            :model-value="currentAnswer"
            @update:model-value="updateAnswer"
            placeholder="Enter your answer..."
            size="lg"
            class="mb-4"
            :ui="{ base: 'font-mono text-lg' }"
          />

          <!-- Math Keyboard -->
          <div class="mt-4">
            <MathKeyboard
              :model-value="currentAnswer"
              @update:model-value="updateAnswer"
            />
          </div>
        </div>

        <!-- Hint (if available) -->
        <div v-if="problem.hint" class="hint-section">
          <UAlert
            icon="i-heroicons-light-bulb"
            color="yellow"
            variant="soft"
            :title="'Hint'"
            :description="problem.hint"
          />
        </div>

        <!-- Validation Feedback -->
        <div v-if="validationMessage" class="feedback-section">
          <UAlert
            :icon="isCorrect ? 'i-heroicons-check-circle' : 'i-heroicons-x-circle'"
            :color="isCorrect ? 'green' : 'red'"
            variant="soft"
            :title="isCorrect ? 'Correct!' : 'Try Again'"
            :description="validationMessage"
          />
        </div>
      </div>

      <template #footer>
        <div class="flex justify-between items-center">
          <UButton @click="handleClose" variant="outline">
            Cancel
          </UButton>
          <UButton
            @click="submitAnswer"
            :disabled="!currentAnswer.trim()"
            :loading="submitting"
          >
            Submit Answer
          </UButton>
        </div>
      </template>
    </UCard>
  </UModal>
</template>

<script setup lang="ts">
import type { Problem, DomainRangeProblem, NotationProblem } from '~/types/game'
import { validateAnswer } from '~/utils/mathValidation'

interface Props {
  problem: Problem
  currentAnswer: string
}

interface Emits {
  (e: 'answer-updated', answer: string): void
  (e: 'answer-submitted'): void
  (e: 'modal-closed'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// State
const submitting = ref(false)
const validationMessage = ref('')
const isCorrect = ref(false)

const getModalTitle = () => {
  if (props.problem.type === 'notation-conversion') {
    const notationProblem = props.problem as NotationProblem
    return `Convert to ${notationProblem.targetNotation.replace('-', ' ')} notation`
  } else {
    const drProblem = props.problem as DomainRangeProblem
    return `Find the ${drProblem.questionType === 'domain' ? 'Domain' : 'Range'}`
  }
}

const updateAnswer = (answer: string) => {
  emit('answer-updated', answer)
  // Clear previous validation when user types
  if (validationMessage.value) {
    validationMessage.value = ''
    isCorrect.value = false
  }
}

const submitAnswer = async () => {
  if (!props.currentAnswer.trim()) return

  submitting.value = true

  try {
    const validation = validateAnswer(props.currentAnswer, props.problem)

    validationMessage.value = validation.message
    isCorrect.value = validation.isCorrect

    if (validation.isCorrect) {
      // Wait a moment to show success message, then emit
      setTimeout(() => {
        emit('answer-submitted')
      }, 1500)
    } else {
      // For incorrect answers, emit immediately to generate new problem
      emit('answer-submitted')
    }
  } catch (error) {
    validationMessage.value = 'An error occurred while validating your answer.'
    isCorrect.value = false
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  emit('modal-closed')
}

// Keyboard shortcuts
onMounted(() => {
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      submitAnswer()
    } else if (event.key === 'Escape') {
      handleClose()
    }
  }

  document.addEventListener('keydown', handleKeydown)

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
})
</script>

<style scoped>
.problem-statement,
.function-display,
.answer-section,
.hint-section,
.feedback-section {
  @apply space-y-2;
}

/* Ensure math expressions are readable */
.font-mono {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}
</style>